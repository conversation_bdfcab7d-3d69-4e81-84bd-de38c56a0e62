import torch
import torch.nn.functional as F
from skimage.metrics import peak_signal_noise_ratio as psnr
import numpy as np
from pytorch_msssim import ssim # Using pytorch_msssim for SSIM

class ImageMetrics:
    def __init__(self, device):
        self.device = device
        self.max_pixel_value_for_metrics = 1.0
    def calculate_psnr(self, pred, target):
        pred_np = pred.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy()
        psnr_vals = []
        for p, t in zip(pred_np, target_np):
            psnr_val = psnr(t, p, data_range=self.max_pixel_value_for_metrics)
            psnr_vals.append(psnr_val)
        return np.mean(psnr_vals)
        
    def calculate_ssim(self, pred, target):
        pred_on_device = pred.to(self.device)
        target_on_device = target.to(self.device)
        ssim_val = ssim(pred_on_device, target_on_device, data_range=self.max_pixel_value_for_metrics, size_average=True) 
        return ssim_val.item() 

    def calculate_metrics(self, pred, target):

        psnr_val = self.calculate_psnr(pred, target)
        ssim_val = self.calculate_ssim(pred, target)
        return psnr_val, ssim_val